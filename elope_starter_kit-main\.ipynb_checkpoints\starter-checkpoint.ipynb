{"cells": [{"cell_type": "markdown", "id": "c83dc86c-3555-46cc-9d8b-7108ffa59254", "metadata": {}, "source": ["# ELOPE: Starter Notebook\n", "\n", "This notebook contains code to get you started working with the data that is coming with the [ELOPE](https://kelvins.esa.int/elope/) challenge on ESA's [Kelvins](https://kelvins.esa.int) platform."]}, {"cell_type": "code", "execution_count": null, "id": "38041c36-8955-4b2e-b562-a6d4ab22f112", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import os\n", "import matplotlib.pyplot as plt\n", "import pandas as pd\n", "from mpl_toolkits.mplot3d import Axes3D\n", "%matplotlib ipympl"]}, {"cell_type": "markdown", "id": "9316ac82-2d07-4d7d-b7e0-2d985a8a117e", "metadata": {}, "source": ["# 1. Opening the data"]}, {"cell_type": "code", "execution_count": null, "id": "c2cf8bea-8e70-46ff-a87d-e9c6f53f0af6", "metadata": {}, "outputs": [], "source": ["# specify path to dataset\n", "datapath = './elope_data'"]}, {"cell_type": "markdown", "id": "b4f74e2b-7d74-4491-a8b1-20f06194cb0d", "metadata": {}, "source": ["### Dataset Folder Structure\n", "\n", "The dataset is organized into two main folders: **train** and **test**. Each folder contains multiple `.npz` files, which correspond to what we call a **landing sequence** or **sequence** for short."]}, {"cell_type": "markdown", "id": "d95305a2-0270-4fea-89c5-f6539df53d27", "metadata": {}, "source": ["Each file (e.g., `0000.npz`, `0001.npz`, etc.) encapsulates the following data:\n", "- **events**: synethtic event data.\n", "- **traj**: trajectory information (state of the lander).\n", "- **timestamps**: time stamps corresponding to the trajectory information.\n", "- **range_meter**: range meter information.\n", "\n", "We inspect `0000.npz` as an example here:"]}, {"cell_type": "code", "execution_count": null, "id": "6a293410-4b11-4045-a21f-adab66b5ecb1", "metadata": {}, "outputs": [], "source": ["fn = os.path.join(datapath, 'train', '0000.npz')\n", "sequence = np.load(fn)"]}, {"cell_type": "code", "execution_count": null, "id": "3c852787-da9a-4ac6-b00d-cba18734a6fb", "metadata": {}, "outputs": [], "source": ["print(sequence.keys())"]}, {"cell_type": "markdown", "id": "efa6c97e-f31f-4463-96e2-ef4d46b1c08f", "metadata": {}, "source": ["## 1.1 Opening and visualizing event data"]}, {"cell_type": "markdown", "id": "10c20dbb-7dfe-4c55-9b03-4f954e71edf9", "metadata": {}, "source": ["Events are stored as 1-dimensional numpy arrays. Each element encodes an event as a 4-tuple `(x, y, p, t)`:\n", "* **x**: x coordinate of event\n", "* **y**: y coordinate of event\n", "* **p**: polarity of event:\n", "   * *True* for positive polarity\n", "   * *False* for negative polarity\n", "* **t**: timestamp of event in µs"]}, {"cell_type": "code", "execution_count": null, "id": "c42eebf8-6315-49ba-a307-4d8ecf3e859a", "metadata": {}, "outputs": [], "source": ["events = sequence['events']\n", "print(\"5 examples of events:\")\n", "for i in range(15465,15470):\n", "    print(events[i])"]}, {"cell_type": "markdown", "id": "02503372-d026-4980-bf9a-3983eb21d738", "metadata": {}, "source": ["For convenience, we will use the `pandas` library to work with a dataframe instead of this representation for the remainder of this notebook."]}, {"cell_type": "code", "execution_count": null, "id": "38c5b143-460b-4eea-8d1d-00bbe87f141b", "metadata": {}, "outputs": [], "source": ["ev_data = pd.DataFrame(events, columns=['x','y','p','t'])"]}, {"cell_type": "code", "execution_count": null, "id": "e212d7ad-af0b-425b-946e-09d8455c6d52", "metadata": {}, "outputs": [], "source": ["print(ev_data)"]}, {"cell_type": "markdown", "id": "88ee02c2-18da-4de4-8da3-54f2f0131a18", "metadata": {}, "source": ["There are multiple ways on how to visualize event data. In the following, we will plot all events that happen between timepoints 0.2 and 0.3 in an **x-y-t**-plot."]}, {"cell_type": "code", "execution_count": null, "id": "75a64bb5-2299-48b0-adc7-71c56ffbe153", "metadata": {}, "outputs": [], "source": ["# filter for positive polarity events and time [0.2, 0.3] seconds\n", "pos_ev = ev_data.loc[(ev_data['p'] == True) & ev_data['t'].between(200000,300000), ['x','y','t']]\n", "\n", "# filter for negative polarity events and time [0.2, 0.3] seconds\n", "neg_ev = ev_data.loc[(ev_data['p'] == False) & ev_data['t'].between(200000,300000), ['x','y','t']]\n", "\n", "# plot in 3D\n", "fig = plt.figure(figsize=(8, 6))\n", "ax = fig.add_subplot(111, projection='3d')\n", "\n", "# plot the points with p == True in red and p == False in blue\n", "ax.scatter(pos_ev['x'], pos_ev['y'], pos_ev['t'], c='red', label='polarity +', s=5)\n", "ax.scatter(neg_ev['x'], neg_ev['y'], neg_ev['t'], c='blue', label='polarity -', s=5)\n", "\n", "# set axis labels and limits\n", "ax.set_xlabel('x')\n", "ax.set_ylabel('y')\n", "ax.set_zlabel('t')\n", "\n", "plt.legend()\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "id": "d1b41546-9ea0-47ee-ba77-d865de5d1963", "metadata": {}, "source": ["The **x-y-t**-plot is a truthful visualization that does not omit any information. However, it is often too cluttered to be helpful. A different visualization is the **event-frame**, which is reminiscent of frame-based RGB cameras. In this visualization, a slice of time (for example 1 second) is flattened into a 2D **x-y**-plot."]}, {"cell_type": "code", "execution_count": null, "id": "7c6467b1-cdf3-4c8f-ad82-7fc1dc286e35", "metadata": {}, "outputs": [], "source": ["def event_frame(ev_data, start_t, acc_t = 1e5):\n", "    \"\"\" Takes pandas dataframe ev_data [x,y,p,t] and generates event-frame by\n", "        slicing beginning from start_t for a duration of acc_t \"\"\"\n", "    assert acc_t >= 0\n", "    assert start_t >= 0\n", "    \n", "    # slice event dataframe\n", "    ev_slice = ev_data.loc[ev_data['t'].between(start_t,start_t + acc_t)]\n", "\n", "    # determine latest events (previous events of the slice will be ignored in this representation)\n", "    max_entries = ev_slice.loc[ev_slice.groupby(['x', 'y'])['t'].idxmax()]\n", "    pos_ev = max_entries.loc[(max_entries['p'] == True), ['x','y']]\n", "    neg_ev = max_entries.loc[(max_entries['p'] == False), ['x','y']]\n", "\n", "    # create empty event frame\n", "    ev_frame = np.zeros([200,200,3], dtype=np.uint8)\n", "\n", "    # to be easy on the eye, we will use a blueish color for negative polarity and white for positive polarity\n", "    ev_frame[pos_ev['x'], pos_ev['y']] = [255, 255, 255]\n", "    ev_frame[neg_ev['x'], neg_ev['y']] = [80, 137, 204]\n", "\n", "    return ev_frame"]}, {"cell_type": "code", "execution_count": null, "id": "34ad2fba-d30f-460d-ba43-4d2adc131a66", "metadata": {}, "outputs": [], "source": ["fig, ax = plt.subplots()\n", "start_t, end_t = 200000, 300000\n", "ev_frame = event_frame(ev_data, start_t, end_t - start_t)\n", "# we need to swap the axis so that `imshow` shows coordinates as one would expect\n", "_ = ax.imshow(np.swapaxes(ev_frame, 0, 1))\n", "ax.set_title(f'event frame [{start_t}, {end_t}] (acc. time: {end_t - start_t})')"]}, {"cell_type": "markdown", "id": "131c9b5e-2b80-4dbe-8b93-ded46705039e", "metadata": {}, "source": ["If you have `ipywidgets` installed, you can step through the event slices with the following code:"]}, {"cell_type": "code", "execution_count": null, "id": "5856eaad-0dee-468e-89a2-c3c6aea93471", "metadata": {}, "outputs": [], "source": ["from ipywidgets import interact\n", "\n", "fig, ax = plt.subplots()\n", "ev_frame = event_frame(ev_data, 200000, 100000)\n", "image = ax.imshow(np.swapaxes(ev_frame, 0, 1))\n", "\n", "max_T = max(ev_data['t'])\n", "\n", "def update_event_frame_plot(start_t, acc_t):\n", "    ev_frame = event_frame(ev_data, start_t, acc_t)\n", "    image.set_data(np.swapaxes(ev_frame, 0, 1))\n", "    ax.set_title(f'event frame [{start_t}, {start_t+acc_t}] (acc. time: {acc_t})')\n", "\n", "interact(update_event_frame_plot, start_t = (0, max_T, max_T / 100), acc_t = (0, 1e6, 1e4))"]}, {"cell_type": "markdown", "id": "b3ceaa7a-3fb5-457e-829f-a52c1bd2e1e8", "metadata": {}, "source": ["## 1.2 Opening and visualizing trajectory data"]}, {"cell_type": "markdown", "id": "358f685a-1b10-40f9-9b6b-7c6ac37a93ee", "metadata": {}, "source": ["The trajectories of the lander are stored as 2-dimensional numpy arrays. Each row corresponds to a **timestamp** (also given as a separate numpy array) and represents what is known about the **state vector** at that time. The state vector is a 12-tuple `(x, y, z, vx, vy, vz, phi, theta, psi, p, q, r)` describing\n", "* **x,y,z**: position in global frame (the attitude z is given negative by convention) [m]\n", "* **vx, vy, vz**: velocity vector [m/s]\n", "* **phi, theta, psi**: roll, pitch, jaw Euler angles [rad]\n", "* **p, q, r**: spacecraft (hence camera) angular velocity [rad/s]"]}, {"cell_type": "markdown", "id": "3cee6ced-7bbe-4ca5-86c3-a0cc199ac7b5", "metadata": {}, "source": ["The position and velocity components are masked out with `np.nan` for each trajectory from the **test** folder. It is your task to recover the velocity from the event-stream and the rest of the provided data.\n", "\n", "Every trajectory is sampled at 120 points in time, which are found in the **timestamp** array. While every trajectoy has the same amount of timestamps, the timestamps themselves differ between trajectories.\n", "\n", "We inspect `0000.npz` again as an example:"]}, {"cell_type": "code", "execution_count": null, "id": "5fe049bc-b0b8-4827-873f-2b7d303b91ae", "metadata": {}, "outputs": [], "source": ["t = sequence['timestamps'] \n", "x = sequence['traj']"]}, {"cell_type": "code", "execution_count": null, "id": "c0c2c3d1-41dc-40a3-a7dc-10f7894b588b", "metadata": {}, "outputs": [], "source": ["nrows, ncols = 4, 3\n", "labels = [['x','y','z'],\n", "          ['vx','vy','vz'],\n", "          ['$\\\\phi$','$\\\\Theta$','$\\\\psi$'],\n", "          ['p','q','r']]\n", "\n", "# Find the minimum and maximum y-values across each row\n", "y_mins, y_maxs = [], []\n", "for row in range(nrows):\n", "    row_data = x.T[row * ncols: (row + 1) * ncols]\n", "    y_mins.append(np.min(row_data))\n", "    y_maxs.append(np.max(row_data))\n", "    \n", "fig, axes = plt.subplots(nrows=nrows, ncols=ncols, figsize=(12, 12), sharex='col')\n", "\n", "# Set y-limits for each row\n", "for row_id in range(nrows):\n", "    for col_id in range(ncols):\n", "        # index in state vector\n", "        idx = row_id * ncols + col_id\n", "        if idx > 12:\n", "            axes[row_id, col_id].remove()\n", "            continue\n", "        \n", "        # plotting\n", "        axes[row_id, col_id].plot([t[0], t[-1]], [0,0], color='lightgrey', linestyle='--')\n", "        axes[row_id, col_id].plot(t, x.T[idx], 'k-')\n", "\n", "        # padding\n", "        h = (y_maxs[row_id] - y_mins[row_id]) / 10\n", "        axes[row_id, col_id].set_ylim(y_mins[row_id] - h, y_maxs[row_id] + h)\n", "        axes[row_id, col_id].set_xlabel(labels[row_id][col_id])"]}, {"cell_type": "markdown", "id": "9470a5ca-8ae7-4bf9-8938-5d2d5cfec3ff", "metadata": {}, "source": ["## 1.3 Opening and visualizing rangemeter"]}, {"cell_type": "markdown", "id": "7461d0eb-03ee-4c2e-a4a4-e300134b9c4d", "metadata": {}, "source": ["Lastly, the sequence contains **rangemeter** information, which is presented as another 2-dimensional numpy-array. The rangemeter measures the distance from the center of the camera to the surface point which corresponds to the position (100, 100) in the camera field of view. In a purely ventral landing with the lander descending the surface normal, the value of the range-meter would be identical to the altitude of the lander. But since our lander maneuvers during its descent, this is not the case for this data. The following visualization shows the rangemeter data against the trajectory $z$ - coordinate (as a proxy for the altitude)."]}, {"cell_type": "code", "execution_count": null, "id": "6daf969f-0adc-45e2-9e2e-a5b4247e5bd7", "metadata": {}, "outputs": [], "source": ["rm = sequence['range_meter']\n", "x = sequence['traj']"]}, {"cell_type": "code", "execution_count": null, "id": "b45bbf5a-c21f-4bc2-898e-fe0f8fb1295a", "metadata": {}, "outputs": [], "source": ["fig, ax = plt.subplots()\n", "rmax = np.max(rm[:,1])\n", "\n", "ax.plot(rm[:,0], -rm[:,1], 'b-')\n", "ax.plot(t, x[:,2], 'r-')\n", "ax.plot([rm[:,0][0], rm[:,0][-1]], [0,0], color='lightgrey', linestyle='--')\n", "\n", "ax.set_ylim(-rmax - (rmax / 10), 0 + rmax / 10)\n", "ax.set_xlabel('time')\n", "ax.set_ylabel('[m]')\n", "ax.set_title('rangemeter vs altitude')\n", "ax.legend(['rangemeter','altitude'])"]}, {"cell_type": "markdown", "id": "f9c10c75-2c0b-4a42-8a94-e49dc933ca6d", "metadata": {}, "source": ["While the rangemeter covers the whole duration of each trajectory, it is sampled at a constant rate of 10Hz. Consequently, the rangemeter comes with **it's own timestamps** that are saved in the first column of its numpy array. The second column gives the actual distance."]}, {"cell_type": "markdown", "id": "9f25f2c4-1a9b-42c4-906f-aad2e6ad41ea", "metadata": {}, "source": ["# 2. Making a submission"]}, {"cell_type": "markdown", "id": "145451c0-ec88-4918-be7e-93e8c6c1b10d", "metadata": {}, "source": ["The goal of the competition is to reconstruct the **velocity** of **all** sequences in the **test** part of the data. We will use `0028.npz` as an example to show that this information has been masked out from the trajectory part:"]}, {"cell_type": "code", "execution_count": null, "id": "02d132f4-395e-4d60-be26-c778491d57de", "metadata": {}, "outputs": [], "source": ["fn = os.path.join(datapath, 'test', '0028.npz')\n", "sequence = np.load(fn)"]}, {"cell_type": "code", "execution_count": null, "id": "8265dfd9-52a9-430d-b7b2-ac1c9b3ac94f", "metadata": {}, "outputs": [], "source": ["# no velocities (showing only first 5 entries)\n", "print(sequence['traj'][:5,3:6])"]}, {"cell_type": "markdown", "id": "fb1ac940-b7dc-4c59-b1f6-c34b345bb2b2", "metadata": {}, "source": ["A submission is a textfile in .JSON format, that contains an object of velocity components for each sequence of the test set. For each sequence, the velocity needs to be sampled at every timestamp accordingly. Thus, the structure of the .JSON-file should look like this:\n", "```\n", "{\n", "  \"28\": {\n", "    \"vx\": [ ... ],\n", "    \"vy\": [ ... ],\n", "    \"vz\": [ ... ]\n", "          },\n", "  \"49\": {\n", "    \"vx\": [ ... ],\n", "    \"vy\": [ ... ],\n", "    \"vz\": [ ... ]\n", "          },\n", "\n", "  ...\n", "\n", "  \"92\": {\n", "    \"vx\": [ ... ],\n", "    \"vy\": [ ... ],\n", "    \"vz\": [ ... ]\n", "          },\n", "}"]}, {"cell_type": "markdown", "id": "21b34e9f-46a9-436a-895d-e165864f9f1c", "metadata": {}, "source": ["We recommend to stick to this schema as close as possible, as our validation is rather **strict**. For example, it will fail should you\n", "* omit a sequence from test\n", "* list a duplicate sequence\n", "* have more sequences than the ones in test\n", "* have invalid or missing values in any of the velocity arrays\n", "* omit any of the three velocity components\n", "* add redundant key-value pairs anywhere\n", "* have too little or too many values in the velocity arrays\n", "* etc."]}, {"cell_type": "markdown", "id": "60dc41ff-b297-4d81-9bb7-ff3ba58263b6", "metadata": {}, "source": ["In the following, we generate a bogus-submission containing random numbers but adhering to the correct format:"]}, {"cell_type": "code", "execution_count": null, "id": "b1bc63ec-2688-4a4a-aebb-d1d22342721e", "metadata": {}, "outputs": [], "source": ["bogus = dict()\n", "for sequence_id in range(28, 93):\n", "    vx = np.random.uniform(-10000, 10000, size=(120,))\n", "    vy = np.random.uniform(-10000, 10000, size=(120,))\n", "    vz = np.random.uniform(-10000, 10000, size=(120,))\n", "\n", "    bogus[sequence_id] = {\"vx\": vx.tolist(), \"vy\": vy.tolist(), \"vz\": vz.tolist()}"]}, {"cell_type": "code", "execution_count": null, "id": "2d125471-0205-4af5-a30a-94330d7ded01", "metadata": {}, "outputs": [], "source": ["# writing submission-file to drive\n", "import json\n", "with open('bogus_submission.json', 'wt') as f:\n", "    json.dump(bogus, f)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 5}