# Event-based Lunar OPtical flow Egomotion estimation

# **Challenge**

### In a nutshell

Estimate **velocities** during lunar descents from event streams. Additionally, a **rangemeter** measurement is provided as well as data from the **Inertial Measurement Unit** (IMU).

### Background

In 2021, the DAVIS240 sensor became the first neuromorphic device to be launched into space and remains operational aboard the International Space Station (ISS), having been delivered as part of the Falcon Neuro Project. This milestone reflects a broader and growing interest in neuromorphic sensing and computing within the aerospace community, which has gained significant momentum over the past decade [1]. Despite this growing enthusiasm, the systematic evaluation and quantification of the benefits offered by event-based cameras in space applications are still in their early stages.

Several promising applications have been proposed for event-based cameras, including star tracking [6], particle detection and tracking [3], spacecraft pose estimation [7], and autonomous landing guidance [3–4]. These proposals leverage the unique advantages of event-based vision sensors, particularly their high dynamic range, low latency, and excellent temporal resolution—characteristics that make them especially suited for the demanding and dynamic conditions encountered in space.

### Event-streams from the Moon

The ELOPE challenge is envisaged as the first of a series of challenges moving towards more and more realistic and challenging event streams. With reference to an earlier version of our dataset production pipeline, tailored at purely synthetic streams [2], the dataset we curated for ELOPE simulates optimal landings of a spacecraft on the challenging South Pole region of the Moon. There is a growing interest in landing near the Moon's South Pole because of its incredible potential for future exploration and sustained human presence. Unlike most of the lunar surface, some areas near the South Pole are in near-constant sunlight, which is ideal for solar power generation. Even more importantly, permanently shadowed regions in nearby craters are believed to contain water ice — a critical resource for life support, fuel production, and more. Access to this ice could make long-term lunar missions far more feasible, turning the South Pole into a strategic hub for both science and future space infrastructure.

The landings provided in the ELOPE dataset correspond to different light conditions and landing sites close to the [Malapert](https://en.wikipedia.org/wiki/Malapert_(crater)) crater. The dataset was created by using digital elevation models of this area, which remain undisclosed and are thus not available for solving the challenge. The only sources of information during the descent are the event streams, telemetry from a simulated IMU and the readings of a rangemeter. The exact descent profiles were found by applying optimal control theory to a deterministically modeled 6DOF lunar landing module with the objective to minimize propellant consumption. Be careful though, as some trajectories might include corrective maneuvers, reflecting the possibility of future landers to select the safest landing spot autonomously.

The event streams were simulated using the *Planet and Asteroid Natural Scene Generation Utility* ([PANGU](https://pangu.software/)) developed by the [University of Dundee's](https://www.dundee.ac.uk/) [Space Technology Centre](http://www.spacetech.dundee.ac.uk/research) and the *realistic dynamic vision sensor event camera data synthesis from frame-based video* [v2e](https://github.com/SensorsINI/v2e) tool, developed by the [Sensors Group](https://sensors.ini.ch/code) in [ETH Zurich](https://ethz.ch/en.html).

### Landing Geometry

The lunar lander in this challenge is equipped with a low-resolution event-based camera mounted on the lunar module. The 3D points of the rugged lunar surface map to a 2D image plane according to the camera geometry. The ego-motion of the lander results in an uninterrupted stream of events in this imaging plane.

Since the event stream alone does not provide information about scale or absolute distances, a rangemeter is used to measure the distance corresponding to the central pixel of the imaging plane.

This figure shows you the reference frames and quantities involved to describe the generic event-based landing of the spacecraft. The camera frame as well as the inertial frame used are visualized. In green, the rangemeter measurement is given.

Check out the [data](https://kelvins.esa.int/elope/data/) page for a precise description of the provided information.

# **Data**

The data for this competition (~800MB) is hosted at the open data repository **Zenodo**:

## Contents

The dataset consists of **93 landing sequences**, each representing a descent towards a different region of the Moon at different times, resulting in challenging lighting conditions and a variety of surface features. The lander is moving from a high gate at about 2-3km to a low gate at about 150m (no touchdown) and might perform any number of maneuvers in between.

Our sequences are split into two categories: **train** and **test**. Each sequence is available as a compressed numpy-file (.npz-file). After loading such a file, for example

```python
sequence = np.load('0000.npz')
```

you can access the sequence like a dictionary to obtain the following information:

- **events:** A sequence of (x,y,p,t) entries, encoding events recorded by a simulated event-camera
    - **x:** event coordinate [0, 199]
    - **y:** event coordinate [0, 199]
    - **p:** polarity [True, False]
    - **t:** timestamp [µs]
- **timestamps:** A sequence of timestamps related to the lander's motion [s]
- **traj:** The trajectory of lander for each of the timestamps as a state-vector (x,y,z,vx,vy,vz,phi,theta,psi,p,q,r)
    - **x,y,z:** position in global frame (the attitude z is given negative by convention) [m]
    - **vx, vy, vz:** velocity vector [m/s]
    - **phi, theta, psi:** roll, pitch, yaw Euler angles [rad]
    - **p, q, r:** corresponding angular velocity [rad/s]
- **rangemeter:** A sequence (t, d) that represents the distance from the camera center to the point on the Moon surface corresponding to the pixel [100,100]
    - **t:** timestamps [s]
    - **d:** distance [m]

The sequences in **train** have full information about the flight path of the lander and can be used as examples to gain insights into the general problem. The sequences in **test** have information about position (x,y,z) and velocity (vx,vy,vz) masked out using **nan**.

The challenge is to reconstruct the missing information and submit it back to us. You can find more details about this process under the [submission rules](https://kelvins.esa.int/elope/submission-rules/) for this challenge.

Your submission will undergo validation and scoring on a **subset of the test data** to grant you a place in the public leaderboard. After the submission period of the competition is over, your submission will be re-evaluated on the full test data to determine the winning team. More about the computation of the score can be found under [scoring](https://kelvins.esa.int/elope/scoring/).

### Getting started

We provide all teams with a starter-notebook that supports you in a variety of tasks, such as

- loading the data
- visualization
- preparing submissions

Please feel free to clone the [related code-repository](https://gitlab.com/EuropeanSpaceAgency/elope_starter_kit) from our official gitlab.

# Scoring

The score of your submission is conceptually a normalized mean-square error of the ground-truth velocity and your estimate of it.

Formally, for a sequence \(i\) over a set of \(T(i)\) timestamps, let \(\mathbf{v}^{(i)}*{x,gt}\), \(\mathbf{v}^{(i)}*{y,gt}\), \(\mathbf{v}^{(i)}*{z,gt}\) be vectors of length \(\lvert T(i)\rvert\) containing the x, y, z components of the lander's velocity for all timestamps. Analogously, let \(\mathbf{v}^{(i)}*{x,est}\), \(\mathbf{v}^{(i)}*{y,est}\), \(\mathbf{v}^{(i)}*{z,est}\) be the estimate of those velocity components as given by your submission. The error \(E(i)\) of sequence \(i\) is defined as

\[
E(i) = \frac{1}{\lvert T(i)\rvert} \cdot
\frac{\sqrt{(v^{(i)}*{x,est} - v^{(i)}*{x,gt})^2
+ (v^{(i)}*{y,est} - v^{(i)}*{y,gt})^2
+ (v^{(i)}*{z,est} - v^{(i)}*{z,gt})^2}}
{z^{(i)}_{gt}},
\]

where \(z^{(i)}_{gt}\) is the ground-truth altitude in sequence \(i\) at the corresponding timestamps.

Let \(I\) be the set of all test sequences and \(P \subset I\) the subset of test sequences for the public leaderboard (about 50% of \(I\)). Your leaderboard score is thus

\[
\text{public score} = \frac{1}{\lvert P\rvert} \sum_{i \in P} E(i)
\]

and your final score (revealed at the end of the competition) is

\[
\text{final score} = \frac{1}{\lvert I\rvert} \sum_{i \in I} E(i).
\]

As the score is based on the error, a **low score is better** than a high score. If you find that confusing, remember that Kelvins is about reaching absolute zero!

# **Rules**

Next to the [Code of Honor](https://kelvins.esa.int/about/), the following rules apply to the competition.

## Eligibility

- **"ELOPE"** competition is open to **everybody**, including space experts, data-mining experts and citizen scientists.
- No nationality restrictions or tariffs apply for the submission of solution files.

## Winner

- The submissions are ranked in the public leaderboard, according to the described score.
- The winners will be invited to collaborate with experts from the European Space Agency for further investigation and academic publication.
- The final ranking and the winner are determined after the end of the submission period. All submissions will be re-evaluated on the entire test set (read more about partial evaluation during the challenge in the *Submission* section below). A new leaderboard will show this final ranking. The team on top of this final leaderboard wins the competition.
- In case of a tie, the tied teams will share their ranking (irrespective of the order at which they are listed on our leaderboards). In other words: there is no tie breaker.
- The final decision on the winner(s) is subject to whether the participants conformed to the rules as checked by ESA's Advanced Concepts Team.

## License

By submitting, the authors grant to the organizers a license to use the submission files for the purpose of verifying and publishing the challenge results.

## Submission

- Before the competition deadline, solutions, notebooks, code snippets may **only be shared publicly**. Private sharing between teams is thus not allowed.
- After the competition deadline, we encourage all participants to publicly share their solutions and code so that anyone interested can reproduce their results.
- The submission limit is one submission within 24 hours per team.
- The leaderboards during the competition use only part of the submission data: we calculate all scores on a subset of the test set.
- The complete submission data will only be evaluated **after the submission period has ended** to determine the final ranking.
- There is only one entry in the leaderboard for each team, showing their best score. If your current submission does not beat your best score, there will be no change in the leaderboard position for your team.

## Teams

- There is no upper limit on the number of members in a team.
- Merging of teams is not allowed.
- The daily submission limit is based on teams, not on users. Thus, each team will have 1 submission per 24h interval.

# Submission Rules

The submission for this challenge consists of **a single text file in JSON** format. Your submission **must** include exactly one entry for every sequence in the **test** category of the data. An entry consists of the sequence identifier and three lists corresponding to the three velocity components *vx*, *vy*, *vz*

. Regarding those velocity components, you **must** provide a value for each timestamp, i.e., the order of the values in the lists corresponds 1:1 to the given timestamps.

So conceptually your submission should look something like this:

```json
{
  "28": {
    "vx": [ ... ],
    "vy": [ ... ],
    "vz": [ ... ]
          },
  "29": {
    "vx": [ ... ],
    "vy": [ ... ],
    "vz": [ ... ]
          },

  ...

  "92": {
    "vx": [ ... ],
    "vy": [ ... ],
    "vz": [ ... ]
          },
}
```

Our submission format is **strict**: anything missing, invalid values, or anything that should not be included like duplicates, wrong sequences, etc. will result in your submission being rejected as **invalid**. This is to your advantage: each team has only one submission per day, with invalid submissions not counting against that limit.

If your submission passes validation, it enters scoring and will count towards your daily submission limit. A public score will be computed according to [scoring](https://kelvins.esa.int/elope/scoring/) and the [leaderboard](https://kelvins.esa.int/elope/leaderboard/leaderboard) will be updated, showing the world who has the most skills when it comes to eloping to the Moon!

If you struggle with the submission format, we recommend taking a look at our [competition starter notebook](https://gitlab.com/EuropeanSpaceAgency/elope_starter_kit) available at GitLab, which includes some code snippets that might help you out with the generation of valid submission files.

# leaderboard

| Name | Submissions | Last Submission | Best Submission | Best Score |
| --- | --- | --- | --- | --- |
| baseline |  |  |  | 0.017938309358526958 |
| Event Meneers | 2 | June 9, 2025, 7:29 p.m. | June 9, 2025, 7:29 p.m. | 0.03214063126631458 |
| JAMBU | 2 | June 3, 2025, 9:14 p.m. | June 3, 2025, 9:14 p.m. | 0.06706738833225133 |
| escobar | 1 | May 28, 2025, 12:31 p.m. | May 28, 2025, 12:31 p.m. | 0.12991590511919274 |

# **Event-based Lunar OPtical flow Egomotion estimation (ELOPE) dataset**

The "ELOPE" dataset is the official dataset of [**ESA's Kelvins** **ELOPE challenge](https://kelvins.esa.int/elope),** created in collaboration with:

- **European Space Agency (ESA)**
- **Delft University of Technology (TU Delft)**
- **University of Adelaide (UoA)**

The purpose of ELOPE is to investigate to what degree it is possible to estimate the **velocity** of a lunar lander during descent from an optical **event stream**. Additionally, a **rangemeter** measurement is provided as well as data from an **Inertial Measurement Unit** (IMU).

**Event cameras** (sometimes called **neuromorphic vision sensors**) allow a novel kind of vision, where pixels correspond to intensity changes and are able to fire independently from each other (asynchronously). The ELOPE dataset contains **synthetic event** **streams** that were generated using high resolution images rendered by the **Planet and Asteroid Natural Scene Generation Utility** ([PANGU](https://pangu.software/)) developed by the [University of Dundee's Space Technology Centre](http://www.spacetech.dundee.ac.uk/research) and post-processed with the [v2e](https://sites.google.com/view/video2events/home) camera emulator developed by [ETH Zurich](https://ethz.ch/en.html).

The dataset consists of **93 landing sequences**, each representing a descent towards a region of the Moon at different times, resulting in challenging lighting conditions and a variety of surface features. The lander is moving from a high gate at about 2-3km to a low gate at about 150m (no touchdown) and might perform any number of maneuvers in between.

Our sequences are split into two categories: **train** and **test**. Each sequence is available as a compressed numpy-file (.npz-file). After loading such a file, for example

```
sequence = np.load('0000.npz')
```

you can access the sequence like a dictionary to obtain the following information:

- **events:** A sequence of (x,y,p,t) entries, encoding events recorded by a simulated event-camera
    - **x:** event coordinate [0, 199]
    - **y:** event coordinate [0, 199]
    - **p:** polarity [True, False]
    - **t:** timestamp [µs]
- **timestamps:** A sequence of timestamps related to the lander's motion [s]
- **traj:** The trajectory of lander for each of the timestamps as a state-vector (x,y,z,vx,vy,vz,phi,theta,psi,p,q,r)
    - **x,y,z:** position in global frame (the attitude z is given negative by convention) [m]
    - **vx, vy, vz:** velocity vector [m/s]
    - **phi, theta, psi:** roll, pitch, yaw Euler angles [rad]
    - **p, q, r:** corresponding angular velocity [rad/s]
- **rangemeter:** A sequence (t, d) that represents the distance from the lander to the pixel [100,100] at short intervals
    - **t:** timestamps [s]
    - **d:** distance [m]

The sequences in **train** have full information about the flight path of the lander and can be used as examples to gain insights into the general problem. The sequences in **test** have information about position (x,y,z) and velocity (vx,vy,vz) masked out using **nan**.

More details about the competition setup and solution evaluation are on the [Kelvins competition platform](https://kelvins.esa.int/elope).

# Getting started

We provide all teams with a starter-notebook that supports you in a variety of tasks, such as

- loading the data
- visualization
- preparing submissions

Please feel free to clone the [related code-repository](https://gitlab.com/EuropeanSpaceAgency/elope_starter_kit) from our official gitlab.