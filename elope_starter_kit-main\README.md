# ELOPE Starter Kit

This is the Starter Kit for the **Event-based Lunar OPtical flow Egomotion estimation (ELOPE) challenge** on ESA's [Kelvins](https://kelvins.esa.int/elope/).
The goal of the ELOPE challenge is to estimate the velocity of a Lunar lander from the corresponding ELOPE dataset.

With the Jupyter notebook in Starter Kit you will be able to:

* open the the ELOPE dataset
* investigate and Visualize the event streams generating during the descent
* investigate the trajectory information and understand how it relates to the rangemeter measurements and events
* generate a valid .json submission-file for evaluation on the Kelvin's platform

## Dependencies

1. You need to download the ELOPE dataset from Zenodo and place it in a folder (i.e. `./elope_dataset`)

2. Only a minimum amount of dependencies are required

  * jupyter
  * numpy
  * matplotlib
  * pandas
  * ipywidgets (optional)

We recommend you create a virtual environment using for example `conda`:

```
conda create -n elope numpy jupyter matplotlib pandas ipywidgets ipympl
```

The rest should be self-explanatory.

## Authors and acknowledgment
The ELOPE competition was made possible by many scientisits who, through the years, have contributed to the development of the ELOPE dataset and challenge. 
The following people are acknowledged (in alphabetical order) for their contributions:

* **Dataset**:
  * Loic Azzalini (European Space Agency) - early version of the dataset creation pipeline.
  * Tat-Jun Chin (University of Adelaide) - competition setup.
  * Ondřej Dvořák (Delft University of Technology) - dataset creation pipeline, dataset curation, competition setup.
  * Pietro Fanti (European Space Agency) - competition setup.
  * Dario Izzo (European Space Agency) - early version of the dataset creation pipeline, competition setup, project supervision.
  * Marcus Märtens (University of Adelaide) - dataset curation, dataset testing, competition setup.
  * Gabriele Meoni (European Space Agency) - early version of the dataset creation pipeline.
  * Leon Williams (European Space Agency) - dataset testing, competition setup.

## License
**Creative Commons Attribution 4.0 International**
The Creative Commons Attribution license allows re-distribution and re-use of a licensed work on the condition that the creator is appropriately credited